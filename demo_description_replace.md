# 详细描述替换功能说明

## 功能概述

在原有的标题替换功能基础上，新增了详细描述替换功能，允许用户一键将AI生成的详细描述建议应用到TAPD缺陷中。

## 实现的功能

### 1. 获取详细描述建议
- **函数**: `get_description_suggestion_by_bug_id(bug_id: str)`
- **位置**: `backend/app/service/bug_evaluation_service.py`
- **功能**: 根据BUG ID从数据库中获取最新的详细描述评估建议

### 2. 详细描述替换处理
- **函数**: `process_description_replace(bug_id: str, operator: str)`
- **位置**: `backend/app/handlers/handlers.py`
- **功能**: 
  - 获取详细描述建议
  - 调用TAPD API更新缺陷的详细描述
  - 记录操作到数据库

### 3. 聊天反馈处理
- **更新**: `process_chat_feedback()` 函数
- **位置**: `backend/app/handlers/handlers.py`
- **功能**: 新增对 `desc_replace` 操作的支持

### 4. 反馈记录更新
- **更新**: `update_bug_evaluation_feedback()` 函数
- **位置**: `backend/app/service/bug_evaluation_service.py`
- **功能**: 支持记录 `desc_replace` 操作到数据库

### 5. UI按钮配置
- **更新**: `build_feedback_buttons_individual()` 函数
- **位置**: `backend/app/service/cards.py`
- **功能**: 在聊天卡片中添加"详细描述替换"按钮

## 使用流程

1. **AI评估**: 系统对缺陷进行评估，生成详细描述建议
2. **卡片推送**: 如果详细描述不通过，推送包含建议和操作按钮的卡片
3. **用户操作**: 用户点击"详细描述替换"按钮
4. **自动替换**: 系统自动将建议的详细描述更新到TAPD
5. **操作记录**: 记录替换操作到数据库

## 按钮配置

```json
{
    "name": "desc_replace",
    "text": "详细描述替换",
    "type": "button",
    "value": "desc_replace",
    "replace_text": "详细描述替换操作已完成",
    "border_color": "#67C23A",
    "text_color": "#67C23A"
}
```

## 数据库字段

- `feedback_action_value`: 记录操作类型（包括新的 `desc_replace`）
- `feedback_operator`: 记录操作人员
- `feedback_timestamp`: 记录操作时间

## API调用

使用现有的 `tap_client.update_bug()` 方法更新TAPD缺陷的详细描述字段。

## 错误处理

- 建议内容为空时的处理
- TAPD API调用失败的处理
- 数据库操作失败的处理
- 详细的日志记录

## 测试

可以使用 `test_description_replace.py` 脚本进行功能测试（需要提供真实的bug_id）。

## 完整的实现文件列表

### 已修改的文件：

1. **backend/app/service/bug_evaluation_service.py**
   - ✅ 新增 `get_description_suggestion_by_bug_id()` 函数
   - ✅ 更新 `update_bug_evaluation_feedback()` 支持 `desc_replace`

2. **backend/app/handlers/handlers.py**
   - ✅ 新增 `process_description_replace()` 函数
   - ✅ 更新 `process_chat_feedback()` 支持 `desc_replace`
   - ✅ 导入 `get_description_suggestion_by_bug_id`

3. **backend/app/service/cards.py**
   - ✅ 更新 `build_feedback_buttons_individual()` 添加详细描述替换按钮

### 新增的文件：

4. **test_description_replace.py**
   - ✅ 测试脚本，用于验证功能

5. **demo_description_replace.md**
   - ✅ 功能说明文档

## 功能对比

| 功能 | 标题替换 | 详细描述替换 |
|------|----------|-------------|
| 获取建议 | `get_title_suggestion_by_bug_id()` | `get_description_suggestion_by_bug_id()` |
| 处理替换 | `process_title_replace()` | `process_description_replace()` |
| API调用 | `update_bug_title()` | `update_bug()` |
| 按钮配置 | `title_replace` | `desc_replace` |
| 数据库记录 | ✅ | ✅ |

## 下一步

功能已完整实现，可以：
1. 部署到测试环境进行验证
2. 使用真实的bug_id测试功能
3. 根据测试结果进行调优
