#!/usr/bin/env python3
"""
测试详细描述替换功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.service.bug_evaluation_service import get_description_suggestion_by_bug_id
from backend.app.handlers.handlers import process_description_replace
from backend.app.utils.logger_util import logger

async def test_get_description_suggestion():
    """测试获取详细描述建议功能"""
    print("=== 测试获取详细描述建议功能 ===")
    
    # 这里需要一个真实的bug_id来测试
    test_bug_id = "1234567"  # 请替换为实际的bug_id
    
    try:
        result = await get_description_suggestion_by_bug_id(test_bug_id)
        if result:
            suggestion, workspace_id = result
            print(f"✅ 成功获取建议:")
            print(f"   Bug ID: {test_bug_id}")
            print(f"   工作空间ID: {workspace_id}")
            print(f"   建议内容: {suggestion[:100]}...")  # 只显示前100个字符
        else:
            print(f"❌ 未找到Bug ID {test_bug_id} 的详细描述建议")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

async def test_process_description_replace():
    """测试详细描述替换处理功能"""
    print("\n=== 测试详细描述替换处理功能 ===")
    
    # 这里需要一个真实的bug_id来测试
    test_bug_id = "1234567"  # 请替换为实际的bug_id
    test_operator = "测试用户"
    
    try:
        result = await process_description_replace(test_bug_id, test_operator)
        if result:
            print(f"✅ 详细描述替换成功:")
            print(f"   Bug ID: {test_bug_id}")
            print(f"   操作人: {test_operator}")
        else:
            print(f"❌ 详细描述替换失败")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

async def main():
    """主测试函数"""
    print("开始测试详细描述替换功能...")
    
    # 测试获取建议
    await test_get_description_suggestion()
    
    # 测试替换处理（注意：这会实际调用TAPD API，请谨慎使用）
    # await test_process_description_replace()
    
    print("\n测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
