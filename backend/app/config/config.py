from dotenv import load_dotenv
import os

print(os.path.dirname(__file__))
load_dotenv(os.path.join(os.path.dirname(__file__), '../../.env'))
## 健康
Robot_WEB_HOOK = os.getenv('Robot_WEB_HOOK')
Report_Robot_WEB_HOOK = os.getenv('Report_Robot_WEB_HOOK')
Robot_WEB_HOOK_TEST = os.getenv('Robot_WEB_HOOK_TEST')

## 医保
YIBAO_ROBOT_WEB_HOOK = os.getenv('YIBAO_ROBOT_WEB_HOOK')

# 医药SAAS
SAAS_ROBOT_WEB_HOOK = os.getenv('SAAS_ROBOT_WEB_HOOK')

# 觅影
MIYING_ROBOT_WEB_HOOK = os.getenv('MIYING_ROBOT_WEB_HOOK')

# 导诊
DAOZHEN_ROBOT_WEB_HOOK = os.getenv('DAOZHEN_ROBOT_WEB_HOOK')

# IMA
IMA_ROBOT_WEB_HOOK = os.getenv('IMA_ROBOT_WEB_HOOK')

# 混元API配置
HUNYUAN_API_URL = os.getenv('HUNYUAN_API_URL', 'https://hunyuan.cloud.tencent.com/hyllm/v1/chat/completions')
HUNYUAN_API_KEY = os.getenv('HUNYUAN_API_KEY')
HUNYUAN_MODEL = os.getenv('HUNYUAN_MODEL', 'hunyuan-t1-latest')
# 评估相关配置
DESCRIPTION_DIMENSIONS = [
    "完整性",
    "清晰度",
    "相关性",
    "详细程度"
]
TITLE_DIMENSIONS = [
    "准确性",
    "清晰性"
]
EXTRACT_IMAGE_MAXNUM = 5

PASSING_THRESHOLD = 0.7

DESCRIPTION_MAX_LENGTH = 1000

# 推送卡片相关配置
ENABLE_DAILY_STATS_PUSH = os.getenv('ENABLE_DAILY_STATS_PUSH', 'true').lower() == 'true'  # 是否启用每日统计推送
SHOW_DIMENSION_FEEDBACK = os.getenv('SHOW_DIMENSION_FEEDBACK', 'true').lower() == 'true'
SHOW_DIMENSION_SUGGEST = os.getenv('SHOW_DIMENSION_SUGGEST', 'true').lower() == 'true'
SHOW_TITLE_FEEDBACK = os.getenv('SHOW_TITLE_FEEDBACK', 'true').lower() == 'true'
SHOW_TITLE_SUGGEST = os.getenv('SHOW_TITLE_SUGGEST', 'true').lower() == 'true'

ENABLE_TEST = os.getenv('ENABLE_TEST', 'true').lower() == 'true'  # 是否启用测试环境
ENABLE_SAVE_DB = os.getenv('ENABLE_SAVE_DB', 'true').lower() == 'true'

# BUG评估脚本时间参数配置
BUG_EVALUATION_START_TIME = os.getenv('BUG_EVALUATION_START_TIME')
BUG_EVALUATION_END_TIME = os.getenv('BUG_EVALUATION_END_TIME')
BUG_EVALUATION_PAST_DAYS = os.getenv('BUG_EVALUATION_PAST_DAYS')
BUG_EVALUATION_YESTERDAY = os.getenv('BUG_EVALUATION_YESTERDAY', 'false').lower() == 'true'
BUG_EVALUATION_TODAY = os.getenv('BUG_EVALUATION_TODAY', 'false').lower() == 'true'

# BUG评估脚本其他参数配置
BUG_EVALUATION_WORKSPACE_ID = os.getenv('BUG_EVALUATION_WORKSPACE_ID')
BUG_EVALUATION_SKIP_MODE = os.getenv('BUG_EVALUATION_SKIP_MODE', 'exists').lower()
BUG_EVALUATION_DRY_RUN = os.getenv('BUG_EVALUATION_DRY_RUN', 'false').lower() == 'true'
BUG_EVALUATION_NOTIFY_ONLY = os.getenv('BUG_EVALUATION_NOTIFY_ONLY', 'false').lower() == 'true'

## workspace_id
JIANKANG_WORKSPACE_ID = "20375472"  # 健康
YIBAO_WORKSPACE_ID = "20426960"  # 医保
YIYAO_SAAS_WORKSPACE_ID = "20452645"  # 医药SAAS
MIYING_WORKSPACE_ID = "20375962"  # 觅影
DAOZHEN_WORKSPACE_ID = "69997217"  #导诊
IMA_WORKSPACE_ID = "20000000"  # IMA (请替换为实际的workspace_id)

# Rag
TRAG_CASE_COLL = os.getenv('TRAG_CASE_COLL', "")
TRAG_WORKSPACE_ID = os.getenv('TRAG_WORKSPACE_ID', "")
TRAG_NAMESPACE = os.getenv('TRAG_NAMESPACE', "")
TRAG_TOKEN = os.getenv('TRAG_TOKEN', "")