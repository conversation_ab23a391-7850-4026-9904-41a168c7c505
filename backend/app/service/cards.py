from typing import List
from backend.app.service.description_socre import DimensionFieldEvaluation
import httpx
import traceback
MAX_RETRIES = 3
RETRY_DELAY = 20  # seconds
import asyncio
from backend.app.utils.logger_util import logger
from backend.app.config.config import <PERSON>_<PERSON>E<PERSON>_HOOK, Robot_WEB_HOOK_TEST,Report_Robot_WEB_HOOK, ENABLE_TEST, MIYING_ROBOT_WEB_HOOK, MIYING_WORKSPACE_ID, SAAS_ROBOT_WEB_HOOK, IMA_ROBOT_WEB_HOOK
from backend.app.service.description_socre import construct_text_message
from backend.app.service.title_score import TitleFieldEvaluation    
from backend.app.service.suggest.field_evaluation_models import SuggestFieldEvaluation
from backend.app.config.config import SHOW_DIMENSION_FEEDBACK, SHOW_TITLE_FEEDBACK, BUG_EVALUATION_WORKSPACE_ID, JIANKANG_WORKSPACE_ID, YIBA<PERSON>_WORKSPACE_ID, YIBAO_ROBOT_WEB_HOOK, YIYAO_SAAS_WORKSPACE_ID, DA<PERSON><PERSON>HEN_WORKSPACE_ID, DAOZHEN_ROBOT_WEB_HOOK, IMA_WORKSPACE_ID
WEB_HOOK_REPORT_URL = Robot_WEB_HOOK_TEST if ENABLE_TEST else Report_Robot_WEB_HOOK

WEB_HOOK_URL = None
if BUG_EVALUATION_WORKSPACE_ID == JIANKANG_WORKSPACE_ID:
    WEB_HOOK_URL = Robot_WEB_HOOK_TEST if ENABLE_TEST else Robot_WEB_HOOK
elif BUG_EVALUATION_WORKSPACE_ID == YIBAO_WORKSPACE_ID:
    WEB_HOOK_URL = Robot_WEB_HOOK_TEST if ENABLE_TEST else YIBAO_ROBOT_WEB_HOOK
elif BUG_EVALUATION_WORKSPACE_ID == YIYAO_SAAS_WORKSPACE_ID:
    WEB_HOOK_URL = Robot_WEB_HOOK_TEST if ENABLE_TEST else SAAS_ROBOT_WEB_HOOK
elif BUG_EVALUATION_WORKSPACE_ID == MIYING_WORKSPACE_ID:
     WEB_HOOK_URL = Robot_WEB_HOOK_TEST if ENABLE_TEST else MIYING_ROBOT_WEB_HOOK
elif BUG_EVALUATION_WORKSPACE_ID == DAOZHEN_WORKSPACE_ID:
     WEB_HOOK_URL = Robot_WEB_HOOK_TEST if ENABLE_TEST else DAOZHEN_ROBOT_WEB_HOOK
elif BUG_EVALUATION_WORKSPACE_ID == IMA_WORKSPACE_ID:
     WEB_HOOK_URL = Robot_WEB_HOOK_TEST if ENABLE_TEST else IMA_ROBOT_WEB_HOOK


async def get_suggest_message(suggest_evaluations: List[SuggestFieldEvaluation]) -> str:
    """构造智能建议内容，仅展示推荐值与用户值不一致的字段"""
    flag = False
    for eval in suggest_evaluations:
        if eval.suggested != eval.actual:
            flag = True
            break
    if not flag:
        return ""

    content = "## 📌 智能建议\n"
    ## content += "<font color='comment'>以下是字段存在优化空间的建议，仅展示与用户值不一致项：</font>\n\n"

    for eval in suggest_evaluations:
        # 推荐值与用户值一致则跳过
        if eval.suggested == eval.actual:
            continue

        content += f"**{eval.field}**\n"

        if eval.suggested:
            content += f"推荐值: <font color='info'>{eval.suggested}</font>"
        if eval.actual:
            content += f"，用户值: <font color='comment'>{eval.actual}</font>\n"
        else:
            content += "，用户值: <font color='warning'>暂无</font>\n"

        if eval.reason:
            reason = ' '.join(line.strip() for line in eval.reason.strip().splitlines() if line.strip())
            content += f"理由: {reason}\n"
        else:
            content += "理由: <font color='comment'>无</font>\n"

        if eval.feedback:
            feedback = ' '.join(line.strip() for line in eval.feedback.strip().splitlines() if line.strip())
            content += f"反馈: <font color='warning'>{feedback}</font>\n"

        content += "\n"

    if content.strip() == "## 智能建议\n<font color='comment'>以下是字段存在优化空间的建议，仅展示与用户值不一致项：</font>":
        return ""
    return content


async def get_title_message(title_evaluations: List[TitleFieldEvaluation]) -> str:
    """构造标题相关的卡片消息内容（支持维度评分）"""
    if not title_evaluations:
        return ""
    content = f"**{title_evaluations[0].field}: **\n"
    for eval in title_evaluations:
        if SHOW_TITLE_FEEDBACK:
            feedback_lines = [line.strip() for line in eval.feedback.strip().splitlines() if line.strip()]
        content += f"** 反馈: ** {' '.join(feedback_lines)}\n"
        content += f"** 建议：**" + eval.suggest + "\n"
    return content



async def get_dimension_message(dimension_evaluations: List[DimensionFieldEvaluation]) -> str:
    """构造维度相关的卡片消息内容"""
    if not dimension_evaluations:
        return ""
    content = f"**{dimension_evaluations[0].field}: **\n"
    for eval in dimension_evaluations:
        if SHOW_DIMENSION_FEEDBACK:
            feedback_lines = [line.strip() for line in eval.feedback.strip().splitlines() if line.strip()]
            content += f"** 反馈: ** {' '.join(feedback_lines)}\n"
        content += f"** 建议：**\n" + eval.suggest + "\n"
    return content

def build_feedback_buttons_individual(bug_id: str, dec_pass, title_pass):
    buttons = []
    if not dec_pass:
        buttons.append({
            "callback_id": bug_id,
            "actions": [
                {
                    "name": "desc_fp",
                    "text": "详细描述误报",
                    "type": "button",
                    "value": "desc_fp",
                    "replace_text": "你已标记为“详细描述误报”",
                    "border_color": "#F56C6C",
                    "text_color": "#F56C6C"
                }
            ]
        })
        buttons.append({
            "callback_id": bug_id,
            "actions": [
                {
                    "name": "desc_invalid",
                    "text": "详细描述建议无效",
                    "type": "button",
                    "value": "desc_invalid",
                    "replace_text": "你已标记为“详细描述建议无效”",
                    "border_color": "#E6A23C",
                    "text_color": "#E6A23C"
                }
            ]
        })
        buttons.append({
            "callback_id": bug_id,
            "actions": [
                {
                    "name": "desc_replace",
                    "text": "详细描述替换",
                    "type": "button",
                    "value": "desc_replace",
                    "replace_text": "详细描述替换操作已完成",
                    "border_color": "#67C23A",
                    "text_color": "#67C23A"
                }
            ]
        })
    if not title_pass:
        buttons.append({
            "callback_id": bug_id,
            "actions": [
                {
                    "name": "title_fp",
                    "text": "标题误报",
                    "type": "button",
                    "value": "title_fp",
                    "replace_text": "你已标记为“标题误报”",
                    "border_color": "#67C23A",
                    "text_color": "#67C23A"
                }
            ]
        })
        buttons.append({
                "callback_id": bug_id,
                "actions": [
                    {
                        "name": "title_invalid",
                        "text": "标题建议无效",
                        "type": "button",
                        "value": "title_invalid",
                        "replace_text": "你已标记为“标题建议无效”",
                        "border_color": "#409EFF",
                        "text_color": "#409EFF"
                    }
                ]
            })
        buttons.append({
                "callback_id": bug_id,
                "actions": [
                    {
                        "name": "title_replace",
                        "text": "标题替换",
                        "type": "button",
                        "value": "title_replace",
                        "replace_text": "标题替换操作已完成",
                        "border_color": "#67C23A",
                        "text_color": "#67C23A"
                    }
                ]
            })
    return buttons



async def send_card_message(
    passed: bool,
    creator: str = "",
    bug_title: str = "",
    bug_link: str = "",
    common_missing: List[str] = [],
    dimension_evalutions: List[DimensionFieldEvaluation] = [],
    title_evaluations: List[TitleFieldEvaluation] = [],
    suggest_evaluations: List[SuggestFieldEvaluation] = [],
    bug_id: str = ""
):
    """发送消息到机器人，使用markdown格式确保内容完整显示，并返回markdown内容"""
    common_missing = common_missing or []
    markdown_content = ""

    if not passed:
        markdown_content += f"<font color='comment'><@{creator}></font> 你有一个缺陷提交不规范，请参考以下建议进行修改。\n\n"
    else:
        markdown_content += f"<font color='comment'><@{creator}></font> 你的缺陷提交已通过规范检查，感谢你的努力！\n\n"

    markdown_content += f"### <font color='info'>缺陷名称：{bug_title}</font>\n\n"
    markdown_content += f"**缺陷链接**: [<font color='info'>点击查看详情</font>]({bug_link})\n\n"

    if common_missing:
        missing_fields = ", ".join(common_missing)
        markdown_content += f"**缺失字段**: <font color='warning'>{missing_fields}</font>\n\n"

    failed_dimenstion_evaluations = [e for e in dimension_evalutions if not e.passed]
    failed_title_evaluations = [e for e in title_evaluations if not e.passed]

    if failed_dimenstion_evaluations:
        content = await get_dimension_message(failed_dimenstion_evaluations)
        markdown_content += content

    if failed_title_evaluations:
        content = await get_title_message(failed_title_evaluations)
        markdown_content += content

    if suggest_evaluations:
        content = await get_suggest_message(suggest_evaluations)
        if content.strip():
            markdown_content += content

    logger.info(f"发送的markdown内容: {markdown_content}")

    result = {"markdown_content": markdown_content}
    dec_pass = len(failed_dimenstion_evaluations) == 0
    title_pass = len(failed_title_evaluations) == 0

    async with httpx.AsyncClient() as client:
        for attempt in range(1, MAX_RETRIES + 1):
            try:
                response = await client.post(
                    WEB_HOOK_URL,
                    json={
                        "msgtype": "markdown",
                        "markdown": {
                            "content": markdown_content,
                            "attachments": build_feedback_buttons_individual(bug_id, dec_pass, title_pass)
                        }
                    }
                )
                response.raise_for_status()
                logger.info("发送卡片返回content: " + str(response.content))
                result.update(response.json())
                return result
            except Exception as e:
                logger.warning(f"发送markdown消息失败 (第 {attempt} 次): {repr(e)}")
                if attempt < MAX_RETRIES:
                    await asyncio.sleep(RETRY_DELAY)
                else:
                    logger.error(traceback.format_exc())
                    break

        # 后备方案：发送text消息
        for attempt in range(1, MAX_RETRIES + 1):
            try:
                text_message = construct_text_message(creator, bug_title, bug_link, common_missing, dimension_evalutions)
                response = await client.post(
                    WEB_HOOK_URL,
                    json={"msgtype": "text", "text": {"content": text_message}}
                )
                response.raise_for_status()
                result.update(response.json())
                result["text_message"] = text_message
                return result
            except Exception as e2:
                logger.warning(f"后备text消息发送失败 (第 {attempt} 次): {repr(e2)}")
                if attempt < MAX_RETRIES:
                    await asyncio.sleep(RETRY_DELAY)
                else:
                    result.update({"error": f"{str(e)}, 后备发送也失败: {str(e2)}"})
                    return result

async def send_message(message: str):
    """发送普通文本消息到机器人（兼容旧代码）"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                WEB_HOOK_URL,
                json={"msgtype": "text", "text": {"content": message}}
            )
            return response.json()
        except Exception as e:
            return {"error": str(e)}


async def send_markdown_message(message: str):
    """发送markdown格式消息到机器人"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                WEB_HOOK_URL,
                json={"msgtype": "markdown", "markdown": {"content": message}}
            )
            return response.json()
        except Exception as e:
            return {"error": str(e)}


async def send_daily_stats_card(stats_data: dict):
    """
    发送每日BUG拦截统计卡片

    Args:
        stats_data: 统计数据字典，包含各项统计指标
    """
    try:
        # 构建统计卡片的markdown内容
        markdown_content = await build_daily_stats_markdown(stats_data)

        # 发送卡片消息
        async with httpx.AsyncClient() as client:
            response = await client.post(
                WEB_HOOK_REPORT_URL,
                json={
                    "msgtype": "markdown",
                    "markdown": {
                        "content": markdown_content
                    }
                }
            )
            response.raise_for_status()
            logger.info(f"每日统计卡片发送成功: {response.status_code}")
            return response.json()

    except Exception as e:
        logger.error(f"发送每日统计卡片失败: {str(e)}")
        logger.error(traceback.format_exc())
        raise


async def build_daily_stats_markdown(stats_data: dict) -> str:
    """
    构建每日统计数据的中文markdown内容（支持多个业务分段展示）

    Args:
        stats_data: 统计数据字典（含总业务 / 健康 / 医保）

    Returns:
        格式化的markdown内容
    """
    date = stats_data.get("统计日期", "未知")

    def format_section(title: str, section: dict) -> str:
        total = section.get("总拦截BUG数量", 0)
        desc_fp = section.get("详细描述误报数量", 0)
        desc_fp_pct = section.get("详细描述误报百分比", 0)
        desc_sugg = section.get("详细描述建议有效数量", 0)
        desc_sugg_pct = section.get("详细描述建议有效百分比", 0)
        title_fp = section.get("标题误报数量", 0)
        title_fp_pct = section.get("标题误报百分比", 0)
        title_sugg = section.get("标题建议有效数量", 0)
        title_sugg_pct = section.get("标题建议有效百分比", 0)
        bug_fixable = section.get("可修复BUG数量", 0)
        bug_fixed = section.get("修复BUG数量", 0)
        bug_fix_ratio = section.get("BUG修复率", 0)

        return (
            f"【{title}】\n"
            f"- 总拦截: {total}\n"
            f"- 详细描述｜误报: {desc_fp}（{desc_fp_pct}%）｜建议有效: {desc_sugg}（{desc_sugg_pct}%）\n"
            f"- 标题｜误报: {title_fp}（{title_fp_pct}%）｜建议有效: {title_sugg}（{title_sugg_pct}%）\n"
            f"- 修复情况｜修复: {bug_fixed} / 可修复: {bug_fixable}（修复率: {bug_fix_ratio}%）\n"
        )

    sections = [
        format_section("总业务", stats_data.get("总业务统计", {})),
        format_section("健康业务", stats_data.get("健康业务统计", {})),
        format_section("医保业务", stats_data.get("医保业务统计", {}))
    ]

    markdown_content = f"{date}｜BUG拦截质量简报\n" + "\n".join(sections)

    return markdown_content